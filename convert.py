import re
import sys

def convert_mysql_to_postgres(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8', errors='replace') as f_in, \
         open(output_file, 'w', encoding='utf-8') as f_out:
        
        for line in f_in:
            # Skip non-INSERT statements
            if not line.strip().upper().startswith('INSERT'):
                continue
                
            # Convert backticks to double quotes
            line = line.replace('`', '"')
            
            # Convert MySQL booleans ( = true, \0 = false)
            line = line.replace("'\\0'", 'FALSE')
            line = line.replace("'\\x01'", 'TRUE')
            
            # Convert \N to NULL
            line = line.replace('\\N', 'NULL')
            
            # Handle any remaining special characters
            line = line.replace('\\', '')
            
            # Write the converted line
            f_out.write(line)
            
            # Print progress
            sys.stdout.write('.')
            sys.stdout.flush()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert.py input.sql output.sql")
        sys.exit(1)
        
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    print(f"Converting {input_file} to PostgreSQL format...")
    convert_mysql_to_postgres(input_file, output_file)
    print(f"\nConversion complete! Output written to {output_file}")