import re
import sys

def convert_mysql_to_postgres(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8', errors='replace') as f_in, \
         open(output_file, 'w', encoding='utf-8') as f_out:
        
        for line in f_in:
            if not line.strip().upper().startswith('INSERT'):
                continue
                
            # Handle backticks
            line = line.replace('`', '"')
            
            # Handle MySQL booleans and empty strings
            line = re.sub(r"'(\\0|\\x00)'", 'FALSE', line)
            line = re.sub(r"'(\\x01|\\1)'", 'TRUE', line)
            line = re.sub(r"''(?=,|\))", 'FALSE', line)  # Convert empty strings to FALSE for boolean columns
            
            # Handle NULL values
            line = line.replace('\\N', 'NULL')
            
            # Handle backslashes
            line = re.sub(r"(?<!\\)\\([^\\])", r"\\\1", line)
            line = line.replace("\\\\", "\\")
            
            # Handle single quotes in values
            line = re.sub(r"'(.*?[^\\])'", lambda m: "'" + m.group(1).replace("'", "''") + "'", line)
            
            f_out.write(line)
            sys.stdout.write('.')
            sys.stdout.flush()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert.py input.sql output.sql")
        sys.exit(1)
        
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    print(f"Converting {input_file} to PostgreSQL format...")
    convert_mysql_to_postgres(input_file, output_file)
    print(f"\nConversion complete! Output written to {output_file}")